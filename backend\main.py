import os
import sys
import logging
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

# Add the parent directory to the path to import ragable
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings
from models import HealthResponse, ErrorResponse
from routers import chat, upload, sessions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Cattalytics RAG API...")
    
    # Validate configuration
    if not settings.openai_api_key:
        logger.error("OPENAI_API_KEY not configured")
        raise RuntimeError("OPENAI_API_KEY is required")
    
    # Set OpenAI API key
    os.environ["OPENAI_API_KEY"] = settings.openai_api_key
    
    logger.info("Application startup complete")
    yield
    logger.info("Application shutdown")

# Create FastAPI app
app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat.router, prefix="/api/v1", tags=["chat"])
app.include_router(upload.router, prefix="/api/v1", tags=["upload"])
app.include_router(sessions.router, prefix="/api/v1", tags=["sessions"])

@app.get("/", response_model=dict)
async def root():
    """Root endpoint"""
    return {
        "message": "Cattalytics RAG API",
        "version": settings.api_version,
        "docs": "/docs"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check Qdrant connection
        from ragable.adapters.qdrant import QdrantAdapter
        qdrant = QdrantAdapter(settings.qdrant_collection, dsn=settings.qdrant_url, api_key=settings.qdrant_api_key)
        qdrant_status = "healthy" if qdrant.store.get_collections() else "unhealthy"
    except Exception as e:
        logger.error(f"Qdrant health check failed: {e}")
        qdrant_status = "unhealthy"
    
    # Check OpenAI connection
    try:
        from ragable.adapters.openai import OpenAIAdapter
        openai_adapter = OpenAIAdapter(model=settings.openai_model)
        # Simple test to verify API key works
        test_embedding = openai_adapter.get_embeddings("test")
        openai_status = "healthy" if test_embedding else "unhealthy"
    except Exception as e:
        logger.error(f"OpenAI health check failed: {e}")
        openai_status = "unhealthy"
    
    return HealthResponse(
        status="healthy" if qdrant_status == "healthy" and openai_status == "healthy" else "degraded",
        services={
            "qdrant": qdrant_status,
            "openai": openai_status
        }
    )

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc) if settings.api_version.endswith("-dev") else None
        ).dict()
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
