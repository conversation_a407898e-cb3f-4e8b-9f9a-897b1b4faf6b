import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import type { ChatMessage, SessionInfo } from '../types'
import { sessionApi } from '../utils/api'

interface SessionContextType {
  currentSessionId: string | null
  messages: ChatMessage[]
  sessions: SessionInfo[]
  isLoading: boolean
  error: string | null
  
  // Actions
  createNewSession: () => Promise<void>
  setCurrentSession: (sessionId: string) => void
  addMessage: (message: ChatMessage) => void
  clearMessages: () => void
  loadSessions: () => Promise<void>
  deleteSession: (sessionId: string) => Promise<void>
  clearError: () => void
}

const SessionContext = createContext<SessionContextType | undefined>(undefined)

export const useSession = () => {
  const context = useContext(SessionContext)
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider')
  }
  return context
}

interface SessionProviderProps {
  children: ReactNode
}

export const SessionProvider: React.FC<SessionProviderProps> = ({ children }) => {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [sessions, setSessions] = useState<SessionInfo[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load sessions on mount
  useEffect(() => {
    loadSessions()
  }, [])

  const createNewSession = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const newSession = await sessionApi.createSession()
      setCurrentSessionId(newSession.session_id)
      setMessages([])
      
      // Refresh sessions list
      await loadSessions()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create session')
    } finally {
      setIsLoading(false)
    }
  }

  const setCurrentSession = (sessionId: string) => {
    setCurrentSessionId(sessionId)
    setMessages([]) // Clear messages, they'll be loaded when needed
  }

  const addMessage = (message: ChatMessage) => {
    setMessages(prev => [...prev, message])
  }

  const clearMessages = () => {
    setMessages([])
  }

  const loadSessions = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const sessionsList = await sessionApi.getAllSessions()
      setSessions(sessionsList)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sessions')
    } finally {
      setIsLoading(false)
    }
  }

  const deleteSession = async (sessionId: string) => {
    try {
      setIsLoading(true)
      setError(null)
      
      await sessionApi.deleteSession(sessionId)
      
      // If we're deleting the current session, clear it
      if (currentSessionId === sessionId) {
        setCurrentSessionId(null)
        setMessages([])
      }
      
      // Refresh sessions list
      await loadSessions()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete session')
    } finally {
      setIsLoading(false)
    }
  }

  const clearError = () => {
    setError(null)
  }

  const value: SessionContextType = {
    currentSessionId,
    messages,
    sessions,
    isLoading,
    error,
    createNewSession,
    setCurrentSession,
    addMessage,
    clearMessages,
    loadSessions,
    deleteSession,
    clearError,
  }

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  )
}
