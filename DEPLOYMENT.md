# Production Deployment Guide

This guide covers deploying the Cattalytics RAG System to production environments.

## 🚀 Deployment Options

### Option 1: <PERSON><PERSON> Compose (Recommended)

Create a `docker-compose.prod.yml` file:

```yaml
version: '3.8'

services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: cattalytics-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cattalytics-backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - QDRANT_URL=http://qdrant:6333
      - CORS_ORIGINS=https://your-domain.com
    depends_on:
      - qdrant
    restart: unless-stopped
    volumes:
      - upload_data:/app/uploads

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cattalytics-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=https://api.your-domain.com
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: cattalytics-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  qdrant_data:
  upload_data:
```

### Option 2: Cloud Deployment

#### AWS Deployment

1. **ECS with Fargate**
   - Use the Docker images with ECS
   - Set up Application Load Balancer
   - Use RDS for session storage (optional)
   - Use S3 for file uploads

2. **EC2 Deployment**
   - Launch EC2 instances
   - Install Docker and Docker Compose
   - Use the docker-compose setup
   - Configure security groups

#### Google Cloud Platform

1. **Cloud Run**
   - Deploy each service as a Cloud Run service
   - Use Cloud SQL for session storage
   - Use Cloud Storage for file uploads

2. **GKE (Kubernetes)**
   - Create Kubernetes manifests
   - Deploy to Google Kubernetes Engine
   - Use persistent volumes for data

## 🔧 Production Configuration

### Environment Variables

Create a production `.env` file:

```env
# Production OpenAI Configuration
OPENAI_API_KEY=your_production_openai_api_key

# Production Qdrant Configuration
QDRANT_URL=http://qdrant:6333
QDRANT_API_KEY=your_qdrant_api_key_if_needed

# Production File Configuration
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=52428800  # 50MB for production
ALLOWED_EXTENSIONS=.txt,.pdf,.docx,.pptx,.odt,.odp

# Production CORS Configuration
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Production Logging
LOG_LEVEL=WARNING
LOG_FILE=/app/logs/app.log

# Production Application Configuration
APP_NAME=Cattalytics RAG System
APP_VERSION=1.0.0
DEBUG=false

# Security
SECRET_KEY=your_very_secure_secret_key_here
```

### Nginx Configuration

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name your-domain.com www.your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com www.your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API routes
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Increase timeout for file uploads
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
        }

        # Health check
        location /health {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Frontend routes
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # File upload size limit
        client_max_body_size 50M;
    }
}
```

## 🔒 Security Considerations

### 1. API Security
- Use HTTPS in production
- Implement rate limiting
- Add API authentication if needed
- Validate all inputs
- Use environment variables for secrets

### 2. File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Store files in secure location
- Implement access controls

### 3. Database Security
- Use strong passwords
- Enable encryption at rest
- Restrict network access
- Regular backups

### 4. Infrastructure Security
- Use firewalls
- Regular security updates
- Monitor logs
- Implement intrusion detection

## 📊 Monitoring and Logging

### Application Monitoring
- Set up health checks
- Monitor API response times
- Track error rates
- Monitor resource usage

### Logging Strategy
- Centralized logging (ELK stack, CloudWatch)
- Structured logging with JSON
- Log rotation and retention
- Security event logging

### Metrics to Track
- Request volume and latency
- Error rates by endpoint
- File upload success/failure rates
- Vector database performance
- OpenAI API usage and costs

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          python -m pytest backend/tests/
          cd frontend && npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          # Build and push Docker images
          # Deploy to your cloud provider
```

## 🚨 Backup and Recovery

### Data Backup
- Regular Qdrant database backups
- File upload backups
- Configuration backups
- Database schema versioning

### Recovery Procedures
- Document recovery steps
- Test recovery procedures
- Maintain backup retention policy
- Monitor backup integrity

## 📈 Scaling Considerations

### Horizontal Scaling
- Load balance multiple backend instances
- Use Redis for session storage
- Implement database read replicas
- Use CDN for static assets

### Performance Optimization
- Enable caching (Redis/Memcached)
- Optimize database queries
- Use connection pooling
- Implement request queuing

## 🔧 Maintenance

### Regular Tasks
- Update dependencies
- Monitor logs for errors
- Check system resources
- Review security alerts
- Update SSL certificates

### Health Checks
- API endpoint availability
- Database connectivity
- File system space
- External service status

---

For additional support, refer to the main README.md or contact the development team.
