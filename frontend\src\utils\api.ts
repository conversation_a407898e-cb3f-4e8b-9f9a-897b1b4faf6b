import axios from 'axios'
import type { 
  ChatRequest, 
  ChatResponse, 
  DocumentUploadResponse, 
  SessionInfo, 
  HealthResponse 
} from '../types'

const API_BASE_URL = '/api/v1'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export const chatApi = {
  sendMessage: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post('/chat', request)
    return response.data
  },

  getSessionHistory: async (sessionId: string) => {
    const response = await api.get(`/chat/sessions/${sessionId}/messages`)
    return response.data
  },

  deleteSession: async (sessionId: string) => {
    const response = await api.delete(`/chat/sessions/${sessionId}`)
    return response.data
  },
}

export const uploadApi = {
  uploadDocument: async (file: File): Promise<DocumentUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 1 minute for file uploads
    })
    return response.data
  },

  uploadDocuments: async (files: File[]): Promise<DocumentUploadResponse[]> => {
    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    
    const response = await api.post('/upload/batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 120000, // 2 minutes for batch uploads
    })
    return response.data
  },

  getUploadStatus: async () => {
    const response = await api.get('/upload/status')
    return response.data
  },
}

export const sessionApi = {
  getAllSessions: async (): Promise<SessionInfo[]> => {
    const response = await api.get('/sessions')
    return response.data
  },

  getSession: async (sessionId: string): Promise<SessionInfo> => {
    const response = await api.get(`/sessions/${sessionId}`)
    return response.data
  },

  createSession: async (): Promise<SessionInfo> => {
    const response = await api.post('/sessions')
    return response.data
  },

  deleteSession: async (sessionId: string) => {
    const response = await api.delete(`/sessions/${sessionId}`)
    return response.data
  },

  deleteAllSessions: async () => {
    const response = await api.delete('/sessions')
    return response.data
  },

  cleanupOldSessions: async (maxAgeHours: number = 24) => {
    const response = await api.post('/sessions/cleanup', null, {
      params: { max_age_hours: maxAgeHours }
    })
    return response.data
  },

  getSessionStats: async () => {
    const response = await api.get('/sessions/stats')
    return response.data
  },
}

export const healthApi = {
  checkHealth: async (): Promise<HealthResponse> => {
    const response = await api.get('/health')
    return response.data
  },
}

export default api
