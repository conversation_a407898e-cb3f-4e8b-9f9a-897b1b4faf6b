@echo off
echo 🐄 Starting Cattalytics RAG System...

REM Check if <PERSON><PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Start Qdrant vector database
echo 🚀 Starting Qdrant vector database...
docker run -d --name cattalytics-qdrant -p 6333:6333 -p 6334:6334 -v %cd%/qdrant_storage:/qdrant/storage qdrant/qdrant

REM Wait for Qdrant to be ready
echo ⏳ Waiting for Qdrant to be ready...
timeout /t 5 /nobreak >nul

REM Start backend
echo 🚀 Starting FastAPI backend...
cd backend

if not exist "venv" (
    echo 📦 Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate

REM Install dependencies
echo 📦 Installing Python dependencies...
pip install -r requirements.txt

REM Copy environment file if it doesn't exist
if not exist ".env" (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ⚠️  Please edit backend/.env and add your OPENAI_API_KEY
)

REM Start backend in background
echo 🚀 Starting backend server...
start /b python main.py

cd ..

REM Start frontend
echo 🚀 Starting React frontend...
cd frontend

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing Node.js dependencies...
    npm install
)

REM Start frontend
echo 🚀 Starting frontend development server...
start /b npm run dev

cd ..

echo.
echo 🎉 Cattalytics RAG System is starting up!
echo.
echo 📊 Services:
echo    • Qdrant Vector DB: http://localhost:6333
echo    • Backend API: http://localhost:8000
echo    • Frontend App: http://localhost:3000
echo    • API Documentation: http://localhost:8000/docs
echo.
echo ⚠️  Make sure to:
echo    1. Add your OpenAI API key to backend/.env
echo    2. Upload some documents before chatting
echo.
echo Press any key to stop all services...
pause >nul

echo 🛑 Stopping services...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
docker stop cattalytics-qdrant >nul 2>&1
docker rm cattalytics-qdrant >nul 2>&1
