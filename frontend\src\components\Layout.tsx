import React, { ReactNode } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Upload, MessageCircle, Activity } from 'lucide-react'
import { useSession } from '../contexts/SessionContext'

interface LayoutProps {
  children: ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation()
  const { sessions, currentSessionId } = useSession()

  const isActive = (path: string) => location.pathname === path

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-primary-600">
                  Cattalytics
                </h1>
                <p className="text-sm text-gray-500">RAG Assistant</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex space-x-8">
              <Link
                to="/upload"
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  isActive('/upload')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Documents
              </Link>
              <Link
                to="/chat"
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  isActive('/chat')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Chat
                {currentSessionId && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    Active
                  </span>
                )}
              </Link>
            </nav>

            {/* Status indicator */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-500">
                <Activity className="w-4 h-4 mr-1 text-green-500" />
                <span>{sessions.length} sessions</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center text-sm text-gray-500">
            <p>© 2024 Cattalytics. All rights reserved.</p>
            <p>Powered by RAG technology</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Layout
