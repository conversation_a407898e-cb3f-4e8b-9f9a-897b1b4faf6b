#!/usr/bin/env python3
"""
Integration test script for Cattalytics RAG System
Tests the basic functionality without requiring OpenAI API key
"""

import os
import sys
import requests
import time
import subprocess
from pathlib import Path

def test_qdrant_connection():
    """Test Qdrant vector database connection"""
    try:
        response = requests.get("http://localhost:6333/health", timeout=5)
        if response.status_code == 200:
            print("✅ Qdrant is running and accessible")
            return True
        else:
            print(f"❌ Qdrant health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Qdrant: {e}")
        return False

def test_backend_health():
    """Test backend health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend is running - Status: {data.get('status', 'unknown')}")
            
            # Check service statuses
            services = data.get('services', {})
            for service, status in services.items():
                icon = "✅" if status == "healthy" else "⚠️"
                print(f"   {icon} {service}: {status}")
            
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def test_backend_endpoints():
    """Test basic backend endpoints"""
    base_url = "http://localhost:8000"
    
    # Test root endpoint
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Root endpoint accessible")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Root endpoint error: {e}")
        return False
    
    # Test sessions endpoint
    try:
        response = requests.get(f"{base_url}/api/v1/sessions", timeout=5)
        if response.status_code == 200:
            print("✅ Sessions endpoint accessible")
        else:
            print(f"❌ Sessions endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Sessions endpoint error: {e}")
        return False
    
    # Test upload status endpoint
    try:
        response = requests.get(f"{base_url}/api/v1/upload/status", timeout=5)
        if response.status_code == 200:
            print("✅ Upload status endpoint accessible")
        else:
            print(f"❌ Upload status endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Upload status endpoint error: {e}")
        return False
    
    return True

def test_frontend_accessibility():
    """Test if frontend is accessible"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend accessibility failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to frontend: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    required_files = [
        "backend/main.py",
        "backend/config.py",
        "backend/models.py",
        "backend/requirements.txt",
        "backend/.env.example",
        "frontend/package.json",
        "frontend/src/App.tsx",
        "frontend/src/main.tsx",
        "ragable/agent.py",
        "ragable/adapters/openai.py",
        "ragable/adapters/qdrant.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files present")
        return True

def test_ragable_integration():
    """Test ragable library integration"""
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.getcwd())
        
        # Test importing ragable modules
        from ragable.agent import get_openai_agent
        from ragable.adapters.openai import OpenAIAdapter
        from ragable.adapters.qdrant import QdrantAdapter
        from ragable.embedders import StandardEmbedder
        
        print("✅ Ragable modules import successfully")
        
        # Test creating agent (without API key)
        try:
            # This will fail without API key, but should not crash
            adapter = OpenAIAdapter()
            print("✅ OpenAI adapter can be instantiated")
        except Exception as e:
            if "api" in str(e).lower():
                print("⚠️  OpenAI adapter requires API key (expected)")
            else:
                print(f"❌ Unexpected error with OpenAI adapter: {e}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import ragable modules: {e}")
        return False
    except Exception as e:
        print(f"❌ Ragable integration test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🧪 Running Cattalytics RAG System Integration Tests\n")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Ragable Integration", test_ragable_integration),
        ("Qdrant Connection", test_qdrant_connection),
        ("Backend Health", test_backend_health),
        ("Backend Endpoints", test_backend_endpoints),
        ("Frontend Accessibility", test_frontend_accessibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Results Summary")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System appears to be working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
