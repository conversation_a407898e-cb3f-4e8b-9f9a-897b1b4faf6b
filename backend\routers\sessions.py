import uuid
import logging
from typing import Dict, List
from fastapi import APIRouter, HTTPException
from datetime import datetime, timedelta

from models import SessionInfo

router = APIRouter()
logger = logging.getLogger(__name__)

# Import sessions from chat router (in production, use shared storage)
from routers.chat import sessions

@router.get("/sessions", response_model=List[SessionInfo])
async def get_all_sessions():
    """Get all active sessions"""
    session_list = []
    for session_id, session_data in sessions.items():
        session_list.append(SessionInfo(
            session_id=session_id,
            created_at=session_data["created_at"],
            message_count=session_data["message_count"],
            last_activity=session_data["last_activity"]
        ))
    
    # Sort by last activity (most recent first)
    session_list.sort(key=lambda x: x.last_activity, reverse=True)
    return session_list

@router.get("/sessions/{session_id}", response_model=SessionInfo)
async def get_session(session_id: str):
    """Get specific session information"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session_data = sessions[session_id]
    return SessionInfo(
        session_id=session_id,
        created_at=session_data["created_at"],
        message_count=session_data["message_count"],
        last_activity=session_data["last_activity"]
    )

@router.post("/sessions", response_model=SessionInfo)
async def create_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    now = datetime.now()
    
    sessions[session_id] = {
        "created_at": now,
        "last_activity": now,
        "messages": [],
        "message_count": 0
    }
    
    logger.info(f"Created new session: {session_id}")
    
    return SessionInfo(
        session_id=session_id,
        created_at=now,
        message_count=0,
        last_activity=now
    )

@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a specific session"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    del sessions[session_id]
    logger.info(f"Deleted session: {session_id}")
    
    return {"message": "Session deleted successfully", "session_id": session_id}

@router.delete("/sessions")
async def delete_all_sessions():
    """Delete all sessions"""
    session_count = len(sessions)
    sessions.clear()
    logger.info(f"Deleted all {session_count} sessions")
    
    return {"message": f"Deleted {session_count} sessions"}

@router.post("/sessions/cleanup")
async def cleanup_old_sessions(max_age_hours: int = 24):
    """Clean up sessions older than specified hours"""
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    sessions_to_delete = []
    
    for session_id, session_data in sessions.items():
        if session_data["last_activity"] < cutoff_time:
            sessions_to_delete.append(session_id)
    
    for session_id in sessions_to_delete:
        del sessions[session_id]
    
    logger.info(f"Cleaned up {len(sessions_to_delete)} old sessions")
    
    return {
        "message": f"Cleaned up {len(sessions_to_delete)} sessions older than {max_age_hours} hours",
        "deleted_sessions": len(sessions_to_delete),
        "remaining_sessions": len(sessions)
    }

@router.get("/sessions/stats")
async def get_session_stats():
    """Get session statistics"""
    if not sessions:
        return {
            "total_sessions": 0,
            "total_messages": 0,
            "average_messages_per_session": 0,
            "oldest_session": None,
            "newest_session": None,
            "most_active_session": None
        }
    
    total_messages = sum(session["message_count"] for session in sessions.values())
    average_messages = total_messages / len(sessions) if sessions else 0
    
    # Find oldest and newest sessions
    oldest_session = min(sessions.items(), key=lambda x: x[1]["created_at"])
    newest_session = max(sessions.items(), key=lambda x: x[1]["created_at"])
    most_active_session = max(sessions.items(), key=lambda x: x[1]["message_count"])
    
    return {
        "total_sessions": len(sessions),
        "total_messages": total_messages,
        "average_messages_per_session": round(average_messages, 2),
        "oldest_session": {
            "session_id": oldest_session[0],
            "created_at": oldest_session[1]["created_at"],
            "message_count": oldest_session[1]["message_count"]
        },
        "newest_session": {
            "session_id": newest_session[0],
            "created_at": newest_session[1]["created_at"],
            "message_count": newest_session[1]["message_count"]
        },
        "most_active_session": {
            "session_id": most_active_session[0],
            "message_count": most_active_session[1]["message_count"],
            "created_at": most_active_session[1]["created_at"]
        }
    }
