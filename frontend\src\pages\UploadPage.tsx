import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, CheckCircle, XCircle, AlertCircle, Trash2 } from 'lucide-react'
import { uploadApi } from '../utils/api'
import type { DocumentUploadResponse } from '../types'

interface UploadedFile {
  file: File
  status: 'pending' | 'uploading' | 'success' | 'error'
  response?: DocumentUploadResponse
  error?: string
  progress?: number
}

const UploadPage: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      file,
      status: 'pending'
    }))
    
    setUploadedFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'application/vnd.oasis.opendocument.text': ['.odt'],
      'application/vnd.oasis.opendocument.presentation': ['.odp']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  })

  const uploadFile = async (fileIndex: number) => {
    const fileToUpload = uploadedFiles[fileIndex]
    if (!fileToUpload || fileToUpload.status === 'uploading') return

    setUploadedFiles(prev => 
      prev.map((f, i) => 
        i === fileIndex ? { ...f, status: 'uploading', progress: 0 } : f
      )
    )

    try {
      const response = await uploadApi.uploadDocument(fileToUpload.file)
      
      setUploadedFiles(prev => 
        prev.map((f, i) => 
          i === fileIndex 
            ? { ...f, status: 'success', response, progress: 100 }
            : f
        )
      )
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      
      setUploadedFiles(prev => 
        prev.map((f, i) => 
          i === fileIndex 
            ? { ...f, status: 'error', error: errorMessage, progress: 0 }
            : f
        )
      )
    }
  }

  const uploadAllFiles = async () => {
    setIsUploading(true)
    
    const pendingFiles = uploadedFiles
      .map((file, index) => ({ file, index }))
      .filter(({ file }) => file.status === 'pending')

    for (const { index } of pendingFiles) {
      await uploadFile(index)
    }
    
    setIsUploading(false)
  }

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const clearAllFiles = () => {
    setUploadedFiles([])
  }

  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'uploading':
        return <div className="w-5 h-5 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
      default:
        return <File className="w-5 h-5 text-gray-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const successfulUploads = uploadedFiles.filter(f => f.status === 'success').length
  const totalChunks = uploadedFiles
    .filter(f => f.response?.chunks_processed)
    .reduce((sum, f) => sum + (f.response?.chunks_processed || 0), 0)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Upload Documents
        </h1>
        <p className="text-gray-600">
          Upload documents to build your knowledge base. Supported formats: TXT, PDF, DOCX, PPTX, ODT, ODP
        </p>
      </div>

      {/* Upload Statistics */}
      {uploadedFiles.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="card p-4">
            <div className="text-2xl font-bold text-primary-600">{uploadedFiles.length}</div>
            <div className="text-sm text-gray-500">Total Files</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-green-600">{successfulUploads}</div>
            <div className="text-sm text-gray-500">Successfully Uploaded</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-accent-600">{totalChunks}</div>
            <div className="text-sm text-gray-500">Text Chunks Processed</div>
          </div>
        </div>
      )}

      {/* Dropzone */}
      <div className="card p-8 mb-6">
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive
              ? 'border-primary-400 bg-primary-50'
              : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          {isDragActive ? (
            <p className="text-lg text-primary-600">Drop the files here...</p>
          ) : (
            <div>
              <p className="text-lg text-gray-600 mb-2">
                Drag & drop files here, or click to select files
              </p>
              <p className="text-sm text-gray-500">
                Maximum file size: 10MB per file
              </p>
            </div>
          )}
        </div>
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="card">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              Files ({uploadedFiles.length})
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={uploadAllFiles}
                disabled={isUploading || uploadedFiles.every(f => f.status !== 'pending')}
                className="btn-primary text-sm"
              >
                {isUploading ? 'Uploading...' : 'Upload All'}
              </button>
              <button
                onClick={clearAllFiles}
                className="btn-secondary text-sm"
              >
                Clear All
              </button>
            </div>
          </div>
          
          <div className="divide-y divide-gray-200">
            {uploadedFiles.map((uploadedFile, index) => (
              <div key={index} className="p-4 flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1">
                  {getStatusIcon(uploadedFile.status)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {uploadedFile.file.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(uploadedFile.file.size)}
                      {uploadedFile.response?.chunks_processed && (
                        <span className="ml-2">
                          • {uploadedFile.response.chunks_processed} chunks
                        </span>
                      )}
                    </p>
                    {uploadedFile.error && (
                      <p className="text-sm text-red-600 mt-1">
                        {uploadedFile.error}
                      </p>
                    )}
                    {uploadedFile.response?.message && uploadedFile.status === 'success' && (
                      <p className="text-sm text-green-600 mt-1">
                        {uploadedFile.response.message}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {uploadedFile.status === 'pending' && (
                    <button
                      onClick={() => uploadFile(index)}
                      className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                    >
                      Upload
                    </button>
                  )}
                  <button
                    onClick={() => removeFile(index)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-8 card p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
          <AlertCircle className="w-5 h-5 text-primary-500 mr-2" />
          How it works
        </h3>
        <div className="text-sm text-gray-600 space-y-2">
          <p>
            1. <strong>Upload documents:</strong> Select or drag files to upload them to the knowledge base.
          </p>
          <p>
            2. <strong>Processing:</strong> Documents are automatically processed and split into chunks for optimal retrieval.
          </p>
          <p>
            3. <strong>Ready to chat:</strong> Once uploaded, you can ask questions about your documents in the Chat section.
          </p>
        </div>
      </div>
    </div>
  )
}

export default UploadPage
