import os
import uuid
import logging
import aiofiles
from typing import List
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import J<PERSON>NResponse

from config import settings
from models import DocumentUploadResponse, ErrorResponse
from ragable.adapters.qdrant import QdrantAdapter
from ragable.embedders import StandardEmbedder

router = APIRouter()
logger = logging.getLogger(__name__)

def get_rag_system():
    """Dependency to get RAG system components"""
    try:
        qdrant = QdrantAdapter(
            settings.qdrant_collection,
            dsn=settings.qdrant_url,
            api_key=settings.qdrant_api_key if settings.qdrant_api_key else None
        )
        embedder = StandardEmbedder(qdrant)
        return qdrant, embedder
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        raise HTTPException(status_code=500, detail="Failed to initialize RAG system")

def validate_file(file: UploadFile) -> None:
    """Validate uploaded file"""
    # Check file extension
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in settings.allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"File type {file_ext} not allowed. Allowed types: {', '.join(settings.allowed_extensions)}"
        )
    
    # Check file size (this is approximate, actual size check happens during upload)
    if hasattr(file, 'size') and file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=400,
            detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
        )

@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    rag_system: tuple = Depends(get_rag_system)
):
    """Upload and process a document for RAG system"""
    qdrant, embedder = rag_system
    
    try:
        # Validate file
        validate_file(file)
        
        # Generate unique file ID
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        safe_filename = f"{file_id}{file_extension}"
        file_path = os.path.join(settings.upload_dir, safe_filename)
        
        # Save file to disk
        total_size = 0
        async with aiofiles.open(file_path, 'wb') as f:
            while chunk := await file.read(8192):  # Read in 8KB chunks
                total_size += len(chunk)
                if total_size > settings.max_file_size:
                    # Clean up partial file
                    await f.close()
                    os.remove(file_path)
                    raise HTTPException(
                        status_code=400,
                        detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
                    )
                await f.write(chunk)
        
        logger.info(f"Saved file {file.filename} as {safe_filename} ({total_size} bytes)")
        
        # Process document through embedder
        try:
            # Count chunks before processing
            text_blob = embedder.extract_text_from_file(file_path)
            chunks = list(embedder.chunk_text(text_blob))
            chunks_count = len(chunks)
            
            # Train the embedder with the document
            embedder.train_from_document(file_path, doc_id=file_id)
            
            logger.info(f"Successfully processed {file.filename} into {chunks_count} chunks")
            
            return DocumentUploadResponse(
                filename=file.filename,
                file_id=file_id,
                status="success",
                message=f"Document processed successfully into {chunks_count} chunks",
                chunks_processed=chunks_count
            )
            
        except Exception as e:
            logger.error(f"Failed to process document {file.filename}: {e}")
            # Clean up file on processing error
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to process document: {str(e)}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during file upload"
        )

@router.post("/upload/batch", response_model=List[DocumentUploadResponse])
async def upload_documents_batch(
    files: List[UploadFile] = File(...),
    rag_system: tuple = Depends(get_rag_system)
):
    """Upload and process multiple documents"""
    if len(files) > 10:  # Limit batch size
        raise HTTPException(
            status_code=400,
            detail="Maximum 10 files allowed per batch upload"
        )
    
    results = []
    for file in files:
        try:
            result = await upload_document(file, rag_system)
            results.append(result)
        except HTTPException as e:
            # Continue processing other files, but record the error
            results.append(DocumentUploadResponse(
                filename=file.filename,
                file_id="",
                status="error",
                message=e.detail,
                chunks_processed=0
            ))
    
    return results

@router.get("/upload/status")
async def get_upload_status():
    """Get upload system status"""
    return {
        "upload_dir": settings.upload_dir,
        "max_file_size": settings.max_file_size,
        "allowed_extensions": settings.allowed_extensions,
        "disk_space_available": True  # Could implement actual disk space check
    }
