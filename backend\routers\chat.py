import uuid
import logging
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime

from config import settings
from models import ChatRequest, ChatResponse, ChatMessage
from ragable.agent import get_openai_agent
from ragable.adapters.qdrant import QdrantAdapter
from ragable.runnable import Runnable

router = APIRouter()
logger = logging.getLogger(__name__)

# In-memory session storage (in production, use Redis or database)
sessions: Dict[str, Dict] = {}

def get_rag_agent():
    """Dependency to get RAG agent"""
    try:
        # Initialize the agent with GPT-4o
        agent = get_openai_agent(
            model_name=settings.openai_model,
            temperature=0.1,
            verbose=True
        )
        
        # Set up Qdrant adapter for document retrieval
        qdrant = QdrantAdapter(
            settings.qdrant_collection,
            dsn=settings.qdrant_url,
            api_key=settings.qdrant_api_key if settings.qdrant_api_key else None
        )
        
        # Create a runnable for document knowledge
        document_knowledge = Runnable(
            Name="Document Knowledge Base",
            Instruction="When the user asks questions about uploaded documents or needs information from the knowledge base",
            Func=qdrant,
            AskLLM=True
        )
        
        # Add system message for Cattalytics context
        agent.add_message(
            "You are a helpful AI assistant for Cattalytics, a leading cattle management software solution. "
            "You have access to uploaded documents and can answer questions based on the knowledge base. "
            "Always be professional, accurate, and helpful. If you don't know something, say so clearly.",
            "system"
        )
        
        # Register the document knowledge runnable
        agent.add_tasks([document_knowledge])
        
        return agent
        
    except Exception as e:
        logger.error(f"Failed to initialize RAG agent: {e}")
        raise HTTPException(status_code=500, detail="Failed to initialize chat system")

def get_or_create_session(session_id: Optional[str] = None) -> str:
    """Get existing session or create new one"""
    if session_id and session_id in sessions:
        # Update last activity
        sessions[session_id]["last_activity"] = datetime.now()
        return session_id
    
    # Create new session
    new_session_id = str(uuid.uuid4())
    sessions[new_session_id] = {
        "created_at": datetime.now(),
        "last_activity": datetime.now(),
        "messages": [],
        "message_count": 0
    }
    return new_session_id

def add_message_to_session(session_id: str, message: ChatMessage):
    """Add message to session history"""
    if session_id in sessions:
        sessions[session_id]["messages"].append(message)
        sessions[session_id]["message_count"] += 1
        sessions[session_id]["last_activity"] = datetime.now()

def get_session_messages(session_id: str) -> List[ChatMessage]:
    """Get messages from session"""
    if session_id in sessions:
        return sessions[session_id]["messages"]
    return []

@router.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    agent = Depends(get_rag_agent)
):
    """Chat with the RAG system"""
    try:
        # Get or create session
        session_id = get_or_create_session(request.session_id)
        
        # Add user message to session
        user_message = ChatMessage(role="user", content=request.message)
        add_message_to_session(session_id, user_message)
        
        # Get conversation history for context
        session_messages = get_session_messages(session_id)
        
        # Add recent conversation history to agent (last 10 messages)
        recent_messages = session_messages[-10:] if len(session_messages) > 10 else session_messages[:-1]  # Exclude current message
        for msg in recent_messages:
            agent.add_message(msg.content, msg.role)
        
        logger.info(f"Processing chat request for session {session_id}: {request.message[:100]}...")
        
        # Get response from agent
        response_text = agent.invoke(request.message)
        
        if not response_text:
            response_text = "I apologize, but I couldn't generate a response. Please try rephrasing your question."
        
        # Add assistant message to session
        assistant_message = ChatMessage(role="assistant", content=response_text)
        add_message_to_session(session_id, assistant_message)
        
        # Extract sources if available (simplified - could be enhanced)
        sources = []
        if hasattr(agent, 'last_runnable_used') and agent.last_runnable_used:
            sources = ["Knowledge Base Documents"]
        
        logger.info(f"Generated response for session {session_id}: {response_text[:100]}...")
        
        return ChatResponse(
            response=response_text,
            session_id=session_id,
            sources=sources if sources else None
        )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An error occurred while processing your message"
        )

@router.get("/chat/sessions/{session_id}/messages")
async def get_session_history(session_id: str):
    """Get chat history for a session"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {
        "session_id": session_id,
        "messages": sessions[session_id]["messages"],
        "message_count": sessions[session_id]["message_count"],
        "created_at": sessions[session_id]["created_at"],
        "last_activity": sessions[session_id]["last_activity"]
    }

@router.delete("/chat/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a chat session"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    del sessions[session_id]
    return {"message": "Session deleted successfully"}

@router.get("/chat/sessions")
async def list_sessions():
    """List all active sessions"""
    session_list = []
    for sid, session_data in sessions.items():
        session_list.append({
            "session_id": sid,
            "created_at": session_data["created_at"],
            "last_activity": session_data["last_activity"],
            "message_count": session_data["message_count"]
        })
    
    return {"sessions": session_list}
