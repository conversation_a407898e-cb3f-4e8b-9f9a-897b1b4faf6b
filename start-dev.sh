#!/bin/bash

# Cattalytics RAG System - Development Startup Script

echo "🐄 Starting Cattalytics RAG System..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start Qdrant vector database
echo "🚀 Starting Qdrant vector database..."
docker run -d --name cattalytics-qdrant -p 6333:6333 -p 6334:6334 \
    -v $(pwd)/qdrant_storage:/qdrant/storage:z \
    qdrant/qdrant

# Wait for Qdrant to be ready
echo "⏳ Waiting for Qdrant to be ready..."
sleep 5

# Check if Qdrant is running
if curl -s http://localhost:6333/health > /dev/null; then
    echo "✅ Qdrant is running"
else
    echo "❌ Qdrant failed to start"
    exit 1
fi

# Start backend
echo "🚀 Starting FastAPI backend..."
cd backend
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
source venv/bin/activate || source venv/Scripts/activate

# Install dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit backend/.env and add your OPENAI_API_KEY"
fi

# Start backend in background
echo "🚀 Starting backend server..."
python main.py &
BACKEND_PID=$!

cd ..

# Start frontend
echo "🚀 Starting React frontend..."
cd frontend

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Start frontend
echo "🚀 Starting frontend development server..."
npm run dev &
FRONTEND_PID=$!

cd ..

echo ""
echo "🎉 Cattalytics RAG System is starting up!"
echo ""
echo "📊 Services:"
echo "   • Qdrant Vector DB: http://localhost:6333"
echo "   • Backend API: http://localhost:8000"
echo "   • Frontend App: http://localhost:3000"
echo "   • API Documentation: http://localhost:8000/docs"
echo ""
echo "⚠️  Make sure to:"
echo "   1. Add your OpenAI API key to backend/.env"
echo "   2. Upload some documents before chatting"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'echo "🛑 Stopping services..."; kill $BACKEND_PID $FRONTEND_PID; docker stop cattalytics-qdrant; docker rm cattalytics-qdrant; exit' INT
wait
