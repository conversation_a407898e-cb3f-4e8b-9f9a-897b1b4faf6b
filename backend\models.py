from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ChatMessage(BaseModel):
    role: str = Field(..., description="Message role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.now)

class ChatRequest(BaseModel):
    message: str = Field(..., description="User message")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")

class ChatResponse(BaseModel):
    response: str = Field(..., description="Assistant response")
    session_id: str = Field(..., description="Session ID")
    sources: Optional[List[str]] = Field(None, description="Source documents used")

class DocumentUploadResponse(BaseModel):
    filename: str = Field(..., description="Uploaded filename")
    file_id: str = Field(..., description="Unique file identifier")
    status: str = Field(..., description="Upload status")
    message: str = Field(..., description="Status message")
    chunks_processed: Optional[int] = Field(None, description="Number of text chunks processed")

class ErrorResponse(BaseModel):
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(default_factory=datetime.now)
    services: Dict[str, str] = Field(..., description="Status of dependent services")

class SessionInfo(BaseModel):
    session_id: str = Field(..., description="Session identifier")
    created_at: datetime = Field(..., description="Session creation time")
    message_count: int = Field(..., description="Number of messages in session")
    last_activity: datetime = Field(..., description="Last activity timestamp")
