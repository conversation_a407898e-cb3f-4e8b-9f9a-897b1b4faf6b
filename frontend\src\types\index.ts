export interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: string
}

export interface ChatRequest {
  message: string
  session_id?: string
}

export interface ChatResponse {
  response: string
  session_id: string
  sources?: string[]
}

export interface DocumentUploadResponse {
  filename: string
  file_id: string
  status: string
  message: string
  chunks_processed?: number
}

export interface SessionInfo {
  session_id: string
  created_at: string
  message_count: number
  last_activity: string
}

export interface HealthResponse {
  status: string
  timestamp: string
  services: {
    qdrant: string
    openai: string
  }
}

export interface ErrorResponse {
  error: string
  detail?: string
}
