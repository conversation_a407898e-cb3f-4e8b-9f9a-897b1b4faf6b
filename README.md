# Cattalytics RAG System

A production-ready Retrieval-Augmented Generation (RAG) chatbot system built for Cattalytics, featuring document upload, processing, and intelligent chat capabilities powered by the enhanced Ragable library.

## 🚀 Features

- **Document Upload**: Support for multiple file formats (PDF, DOCX, PPTX, TXT, ODT, ODP)
- **Intelligent Chat**: GPT-4o powered conversations with document context
- **Session Management**: Persistent chat sessions with history
- **Modern UI**: React-based frontend with Cattalytics branding
- **Production Ready**: Comprehensive error handling, logging, and validation
- **Vector Search**: Qdrant-powered semantic document retrieval

## 🏗️ Architecture

### Backend (FastAPI)
- **FastAPI**: Modern Python web framework
- **Ragable Integration**: Enhanced ragable library with GPT-4o
- **Qdrant**: Vector database for document storage and retrieval
- **OpenAI**: GPT-4o for chat and text-embedding-3-small for embeddings

### Frontend (React + TypeScript)
- **React 18**: Modern React with hooks and context
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling with Cattalytics theme
- **Vite**: Fast development and build tooling

## 📋 Prerequisites

- **Python 3.8+**
- **Node.js 16+**
- **Docker** (for Qdrant vector database)
- **OpenAI API Key**

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
start-dev.bat
```

**Linux/Mac:**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

### Option 2: Manual Setup

1. **Start Qdrant Database**
```bash
docker run -d --name cattalytics-qdrant -p 6333:6333 -p 6334:6334 \
  -v $(pwd)/qdrant_storage:/qdrant/storage:z \
  qdrant/qdrant
```

2. **Setup Backend**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env and add your OPENAI_API_KEY
python main.py
```

3. **Setup Frontend**
```bash
cd frontend
npm install
npm run dev
```

## 🔧 Configuration

### Backend Configuration (`backend/.env`)
```env
OPENAI_API_KEY=your_openai_api_key_here
QDRANT_URL=http://127.0.0.1:6333
QDRANT_API_KEY=
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=.txt,.pdf,.docx,.pptx,.odt,.odp
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

## 📚 API Documentation

Once the backend is running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Key Endpoints

- `POST /api/v1/upload` - Upload documents
- `POST /api/v1/chat` - Send chat messages
- `GET /api/v1/sessions` - List chat sessions
- `GET /api/v1/health` - Health check

## 🎯 Usage

1. **Start the System**: Use the startup scripts or manual setup
2. **Add OpenAI API Key**: Edit `backend/.env` with your OpenAI API key
3. **Upload Documents**: Go to http://localhost:3000/upload and upload your documents
4. **Start Chatting**: Go to http://localhost:3000/chat and ask questions about your documents

## 🏢 Cattalytics Branding

The system is themed with Cattalytics branding:
- **Primary Colors**: Blue tones (#0ea5e9, #0284c7)
- **Secondary Colors**: Gray scale for UI elements
- **Accent Colors**: Orange tones for highlights
- **Typography**: Inter font family
- **Logo**: Cattalytics branding in header

---

## Original Ragable Library Documentation

### About Ragable
Ragable is an AI library that helps you build multi-turn Chatbots with ease, the library provides several easy-to-use classes to help you build "agent style" workflows. The Agent will first analyze the user's input and then determine what function to invoke (known as Runnable's).

Each **Runnable** takes the following arguments:
 - **Name**: a unique identifier that describes this runnable.
 - **Instruction**: Instruct the LLM on when it should invoke this runnable.
 - **Func**: Any Python function you want to run if the model chooses to execute this Runnable. The function should return text as its output. You can also pass a vector store to this function, when doing so, the Runnable will query the vector store for similar documents and pass it on to the LLM for further reasoning.
 - **AskLLM**: By default the LLM will simply execute the function and return its output to the user. If you prefer to pass on the function output to the LLM for further reasoning, set this to **True**.
 - **Params**: Any local data you want to make available to the Runnable function. This can be used to pass a user object or session information. Params are not sent to the LLM.

```python
from ragable.agent import get_openai_agent
from ragable.runnable import Runnable
from ragable.adapters.qdrant import QdrantAdapter
from ragable.embedders import StandardEmbedder
import os

# See examples.py for more details
    agent = get_openai_agent()
    qdrant = QdrantAdapter("ragable_documents")
    embedder = StandardEmbedder(qdrant)
    embedder.train_from_document("./testdata/bulbasaur.txt")

    bulbasaur_knowledge = Runnable(
        Name="Information about bulbasaur",
        Instruction="When the human asks about bulbasaur",
        Func=qdrant
    )

    agent.add_tasks([
        legendary_pokemon,
        php_strings,
        bulbasaur_knowledge
    ])

    questions = [
        "What is a legendary pokemon?",
        "How to perform a string replace in PHP?",
        "How to find a string in another string in PHP?",
        "Which Pokemon are the evolved forms of bulbasaur?"
    ]

    for q in questions:
        response = agent.invoke(q)
        print(response)
```
## How to install?

You can GIT clone this repository and run:

    pip install requirements.txt

Thereafter, you can then import any of the Ragable classes into your Python projects.

**Note:** If you plan on using a vector store with Ragable, we currently support Qdrant. You can use their docker image to setup Qdrant as follows:
```python
docker run -dit --name raggable-qdrant -p 6333:6333 -p 6334:6334 \
-v $(pwd)/qdrant_storage:/qdrant/storage:z \
qdrant/qdrant
```

## Bulk import and vectorize documents

To bulk import documents into Qdrant, simply run the following with the relevant folder path and collection name:
```python
 python document_feeder.py --folder ./documents/  --collection pokemon
```
